const { searchPortfolioAssets } = require('@/lib/api.js')
module.exports = {
  api: {
    input: "https://api.fatbot.devel.cleevio.dev/api-docs",
    output: {
      client: "react-query",
      target: "./src/lib/api.ts",
      mode: "single",
      prettier: true,

      override: {
        mutator: {
          path: "./src/lib/axios-instance.ts",
          name: "customInstance",
        },
        query: {
          signal: false,
        },
        operations: {
          searchUserTransactionOfAllTokenV3: {
            query: {
              useQuery: true,
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          searchUserBotsTransactions: {
            query: {
              useQuery: true,
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          searchUserMarketPositionV3: {
            query: {
              useQuery: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          searchUserBotMarketPositions: {
            query: {
              useQuery: true,
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          getAllUserWalletsV3: {
            query: {
              useQuery: true,
            },
          },
          getBotWithdrawGasEstimation: {
            query: {
              useQuery: true,
            },
          },
          getHotTokensV2: {
            query: {
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          getHotTokensV2Public: {
            query: {
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          getPredictedTokenAmountOnBuyV2: {
            query: {
              useQuery: true,
            },
          },
          getPredictedEthAmountOnSellV2: {
            query: {
              useQuery: true,
            },
          },
          searchUserWalletCurrencyPositions: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          getMaxBuyV2: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          getTaxV2: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          getTokenPriceChartV2: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          getContinuousTokenPriceChartV2: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          getGasEstimationUsdV2: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          botsLeaderboard: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          botsLeaderboard_1: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          compareBots: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          searchUserBotDetail: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          botsOverview: {
            query: {
              useQuery: true,
              signal: false,
            },
          },
          searchBotMarketPositions: {
            query: {
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          searchUserFattyCards: {
            query: {
              useQuery: true,
            }
          },
          getTokenHistoricalCandleChart: {
            query: {
              signal: false,
              useQuery: true,
            },
          },
          searchUserLeaderboards: {
            query: {
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
          searchPortfolioAssets: {
            query: {
              useInfinite: true,
              useInfiniteQueryParam: "lastId",
              signal: false,
            },
          },
        },
      },
    },
  },
};
