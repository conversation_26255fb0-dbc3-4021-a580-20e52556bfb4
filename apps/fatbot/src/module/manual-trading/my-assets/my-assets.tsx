'use client';

import { useTranslations } from 'next-intl';
import { match } from 'ts-pattern';

import { DataListHeader } from '@/components/data-list/data-list-header';
import { MyAssetsHeader } from '@/components/data-list/my-assets-list/my-assets-header';
import { MyAssetsList } from '@/components/data-list/my-assets-list/my-assets-list';
import { MyAssetsRowSkeleton } from '@/components/data-list/my-assets-list/my-assets-row-skeleton';
import { ErrorMessage } from '@/components/error-message';
import { ROUTES } from '@/constants/routes';
import { getErrorMessage } from '@/lib/get-error-message';
import {
  isQueryEmptyData,
  isQueryError,
  isQueryLoading,
} from '@/lib/query-pattern';
import { DepositWallet } from '@/module/manual-trading/deposit-wallet/deposit-wallet';
import { useSearchPortfolioAssets } from '@/lib/api'

export const MyAssets: React.FC = () => {
  const t = useTranslations();
  const myAssets = useSearchPortfolioAssets({ useSelectedChains: true }, { size: 5 });

  console.log(myAssets.data);

  return (
    <div className="grid grid-cols-1 gap-2">
      <DataListHeader
        link={myAssets.error ? '' : ROUTES.MY_ASSETS.ROOT}
        title={t('manual-trading.my-tokens')}
      />
      {match(myAssets)
        .when(isQueryLoading, () => (
          <>
            <MyAssetsHeader className="hidden sm:grid" />
            <MyAssetsRowSkeleton />
            <MyAssetsRowSkeleton />
          </>
        ))
        .when(isQueryEmptyData, () => (
          <ErrorMessage className="text-left">
            {t('manual-trading.errors.no-data')}
          </ErrorMessage>
        ))
        .when(isQueryError, ({ error }) => (
          <ErrorMessage className="text-left" variant="error">
            {getErrorMessage(error) ??
              t('manual-trading.errors.loading-failed')}
          </ErrorMessage>
        ))
        .otherwise(({ data }) => (
          <>
            <MyAssetsHeader className="hidden sm:grid" />
            <MyAssetsList data={[]} />
          </>
        ))}
      <DepositWallet />
    </div>
  );
};
