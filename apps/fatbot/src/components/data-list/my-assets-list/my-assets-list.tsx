'use client';

import { usePathname, useRouter } from 'next/navigation';

import { MyAssetsRow } from './my-assets-row';

import { ROUTES } from '@/constants/routes';
import { isNativeToken } from '@/module/assets/utils';
import type { Chain } from '@/lib/api'

interface Props {
  data: {
    chain: Chain;
    currentPriceUsd: string;
    currentValueNative: string;
    currentValueUsd: string;
    id: string;
    oneDayChangeFraction: string;
    oneHourChangeFraction: string;
    pnlFraction: string;
    pnlUsd: string;
  }[];
}

export const MyAssetsList: React.FC<Props> = ({ data }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="grid gap-2">
      {data.map((item) => (
        <MyAssetsRow
          key={item.id}
          chain={item.chain}
          currentValueChangeFraction={item.currentValueChangeFraction}
          currentValueChangeUsd={item.currentValueChangeUsd}
          currentValueUsd={item.currentValueUsd}
          image={item.tokenInfo?.imageUrl}
          oneDayChangeFraction={item.oneDayChangeFraction}
          oneHourChangeFraction={item.oneHourChangeFraction}
          pricePerTokenInUsd='0'
          tokenAmount={item.tokenNativeAmount}
          tokenDetailUrl={item.tokenDetailUrl}
          tokenName={item.tokenName}
          tokenSymbol={item.tokenSymbol}
          onClick={
            !isNativeToken(item)
              ? () => {
                  router.push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(item.tokenAddress, item.tokenChain, pathname));
                }
              : undefined
          }
        />
      ))}
    </div>
  );
};
